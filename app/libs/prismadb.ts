import { PrismaClient } from '@prisma/client'

declare global {
  var prisma: PrismaClient | undefined
}

const client =
  globalThis.prisma ||
  new PrismaClient({
    log:
      process.env.NODE_ENV === 'development'
        ? ['query', 'info', 'warn', 'error']
        : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

if (process.env.NODE_ENV !== 'production') globalThis.prisma = client

// --- DragTree / DragTreeNode alignment middleware ---

// Lightweight helper to avoid circular dependency with validation-utils
const extractIdsFromTreeStructure = (treeStructure: any): Set<string> => {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()
  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }
  if (!root_id || !hierarchy) return new Set()
  const ids = new Set<string>()
  const stack: string[] = [root_id]
  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }
  return ids
}

const mutatingActions = new Set([
  'create',
  'createMany',
  'update',
  'updateMany',
  'delete',
  'deleteMany',
])

client.$use(async (params, next) => {
  const result = await next(params)

  if (
    !(params.model === 'DragTree' || params.model === 'DragTreeNode') ||
    !mutatingActions.has(params.action as string)
  ) {
    return result
  }

  let treeId: string | undefined
  if (params.model === 'DragTree') {
    treeId = params.args?.where?.id as string | undefined
  } else if (params.model === 'DragTreeNode') {
    treeId = (params.args?.data?.drag_tree_id ||
      params.args?.where?.drag_tree_id) as string | undefined
    if (!treeId && params.args?.where?.id) {
      const node = await client.dragTreeNode.findUnique({
        where: { id: params.args.where.id as string },
        select: { drag_tree_id: true },
      })
      treeId = node?.drag_tree_id
    }
  }

  if (!treeId) return result

  const isValid = await client.$transaction(async tx => {
    const [tree, activeNodes] = await Promise.all([
      tx.dragTree.findUnique({
        where: { id: treeId },
        select: { tree_structure: true },
      }),
      tx.dragTreeNode.findMany({
        where: { drag_tree_id: treeId, status: 'ACTIVE' },
        select: { id: true },
      }),
    ])
    if (!tree || !tree.tree_structure) return true
    const idsFromTree = extractIdsFromTreeStructure(tree.tree_structure)
    const activeIdSet = new Set<string>(activeNodes.map(n => n.id))
    if (idsFromTree.size !== activeIdSet.size) return false
    for (const id of Array.from(idsFromTree)) {
      if (!activeIdSet.has(id)) return false
    }
    return true
  })

  if (!isValid) {
    throw new Error(
      'Drag tree hierarchy and active node set are out of sync – mutation cancelled.'
    )
  }

  return result
})

export default client
