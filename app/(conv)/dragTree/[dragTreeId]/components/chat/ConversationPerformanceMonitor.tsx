'use client'

import React, { useEffect, useState } from 'react'

type PerformanceMetrics = {
  conversationLoadTime: number
  messageCount: number
  cacheHit: boolean
  apiResponseTime: number
  payloadSize: number
}

type ConversationPerformanceMonitorProps = {
  conversationId?: string
  isLoading: boolean
  messageCount: number
  onMetricsCollected?: (metrics: PerformanceMetrics) => void
}

/**
 * Performance monitoring component for conversation loading
 * Tracks load times, cache hits, and payload sizes for optimization
 */
export default function ConversationPerformanceMonitor({
  conversationId,
  isLoading,
  messageCount,
  onMetricsCollected,
}: ConversationPerformanceMonitorProps) {
  const [startTime, setStartTime] = useState<number | null>(null)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)

  // Track loading start
  useEffect(() => {
    if (isLoading && !startTime) {
      setStartTime(performance.now())
    }
  }, [isLoading, startTime])

  // Track loading completion
  useEffect(() => {
    if (!isLoading && startTime && messageCount > 0) {
      const loadTime = performance.now() - startTime
      
      // Extract performance data from console logs or network requests
      const performanceEntries = performance.getEntriesByType('navigation')
      const networkEntries = performance.getEntriesByType('resource')
      
      // Find conversation API call
      const conversationApiCall = networkEntries.find(entry =>
        entry.name.includes(`/api/aipane/conversations/${conversationId}`)
      ) as PerformanceResourceTiming | undefined

      const newMetrics: PerformanceMetrics = {
        conversationLoadTime: loadTime,
        messageCount,
        cacheHit: loadTime < 100, // Assume cache hit if very fast
        apiResponseTime: conversationApiCall?.duration || 0,
        payloadSize: conversationApiCall?.transferSize || 0,
      }

      setMetrics(newMetrics)
      onMetricsCollected?.(newMetrics)
      
      // Log performance metrics
      console.log('📊 [ConversationPerformanceMonitor] Metrics:', {
        conversationId,
        loadTime: `${loadTime.toFixed(2)}ms`,
        messageCount,
        cacheHit: newMetrics.cacheHit,
        apiTime: `${newMetrics.apiResponseTime.toFixed(2)}ms`,
        payloadSize: `${(newMetrics.payloadSize / 1024).toFixed(2)}KB`,
      })

      // Reset for next measurement
      setStartTime(null)
    }
  }, [isLoading, startTime, messageCount, conversationId, onMetricsCollected])

  // Development-only performance display
  if (process.env.NODE_ENV !== 'development' || !metrics) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded font-mono z-50">
      <div className="font-bold mb-1">Performance Metrics</div>
      <div>Load Time: {metrics.conversationLoadTime.toFixed(2)}ms</div>
      <div>Messages: {metrics.messageCount}</div>
      <div>Cache Hit: {metrics.cacheHit ? '✅' : '❌'}</div>
      <div>API Time: {metrics.apiResponseTime.toFixed(2)}ms</div>
      <div>Payload: {(metrics.payloadSize / 1024).toFixed(2)}KB</div>
    </div>
  )
}

/**
 * Hook for collecting performance metrics across the app
 */
export function useConversationPerformanceMetrics() {
  const [allMetrics, setAllMetrics] = useState<PerformanceMetrics[]>([])

  const addMetrics = (metrics: PerformanceMetrics) => {
    setAllMetrics(prev => [...prev.slice(-9), metrics]) // Keep last 10 measurements
  }

  const getAverageMetrics = () => {
    if (allMetrics.length === 0) return null

    return {
      avgLoadTime: allMetrics.reduce((sum, m) => sum + m.conversationLoadTime, 0) / allMetrics.length,
      avgApiTime: allMetrics.reduce((sum, m) => sum + m.apiResponseTime, 0) / allMetrics.length,
      avgPayloadSize: allMetrics.reduce((sum, m) => sum + m.payloadSize, 0) / allMetrics.length,
      cacheHitRate: allMetrics.filter(m => m.cacheHit).length / allMetrics.length,
      totalMeasurements: allMetrics.length,
    }
  }

  return {
    allMetrics,
    addMetrics,
    getAverageMetrics,
  }
}
